import $2ListenID from "./ListenID";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M33_Pop_Revive extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
