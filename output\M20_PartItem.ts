import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2Listen<PERSON> from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2RBadgeModel from "./RBadgeModel";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_PartItem extends cc.Component {
    // TODO: 添加属性和方法
}
