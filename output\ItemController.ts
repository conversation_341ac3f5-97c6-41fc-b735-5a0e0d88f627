import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2AlertManager from "./AlertManager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2ItemModel from "./ItemModel";

const { ccclass, property, menu } = cc._decorator;

export default class ItemController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
