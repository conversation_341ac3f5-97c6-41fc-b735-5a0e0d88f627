import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2GoodsUIItem from "./GoodsUIItem";

const { ccclass, property, menu } = cc._decorator;

export default class ItemModel extends $2MVC.MVC.BaseModel {
    // TODO: 添加属性和方法
}
