import $2Cfg from "./Cfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2GameSeting from "./GameSeting";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2PropertyVo from "./PropertyVo";
import $2MBackpackHero from "./MBackpackHero";
import $2M20Prop from "./M20Prop";
import $2M20Prop_Equip from "./M20Prop_Equip";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20Prop_Gemstone extends $2M20Prop.default {
    // TODO: 添加属性和方法
}
