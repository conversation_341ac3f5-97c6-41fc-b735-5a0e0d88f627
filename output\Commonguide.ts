import $2CallID from "./CallID";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2AlertManager from "./AlertManager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Commonguide extends $2MVC.MVC.BaseView {
    // TODO: 添加属性和方法
}
