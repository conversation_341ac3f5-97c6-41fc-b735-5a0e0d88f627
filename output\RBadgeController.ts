import $2CallID from "./CallID";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2RBadgeModel from "./RBadgeModel";
import $2RBadgePoint from "./RBadgePoint";

const { ccclass, property, menu } = cc._decorator;

export default class RBadgeController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
