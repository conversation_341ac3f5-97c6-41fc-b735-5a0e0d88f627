import $2Cfg from "./Cfg";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2Intersection from "./Intersection";
import $2GameUtil from "./GameUtil";
import $2Monster from "./Monster";
import $2Game from "./Game";
import $2SkillManager from "./SkillManager";
import $2MMGuards from "./MMGuards";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MMGMonster extends $2Monster.Monster {
    // TODO: 添加属性和方法
}
