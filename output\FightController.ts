import $2CallID from "./CallID";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2KnapsackVo from "./KnapsackVo";
import $2NodePool from "./NodePool";
import $2FightModel from "./FightModel";
import $2Game from "./Game";

const { ccclass, property, menu } = cc._decorator;

export default class FightController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
