import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2ShareButton from "./ShareButton";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2AlertManager from "./AlertManager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class GiftPackView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
