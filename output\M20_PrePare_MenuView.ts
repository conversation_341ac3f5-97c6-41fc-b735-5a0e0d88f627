import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2BaseSdk from "./BaseSdk";
import $2GameUtil from "./GameUtil";
import $2RecordVo from "./RecordVo";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_PrePare_MenuView extends $2MVC.MVC.BaseView {
    // TODO: 添加属性和方法
}
