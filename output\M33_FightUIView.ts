import $2AutoAmTool from "./AutoAmTool";
import $2Cfg from "./Cfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2L<PERSON>enID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2FightUIView from "./FightUIView";
import $2Game from "./Game";
import $2MChains from "./MChains";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M33_FightUIView extends $2FightUIView.FightUIView {
    // TODO: 添加属性和方法
}
