const FinalEnhancedConverter = require('./final_enhanced_converter.js');
const fs = require('fs');
const path = require('path');

// 创建转换器实例
const converter = new FinalEnhancedConverter();

// 配置
const inputDir = './scripts';
const outputDir = './output';

console.log('🚀 开始使用改进的转换器批量转换所有文件...\n');
console.log(`📁 输入目录: ${inputDir}`);
console.log(`📁 输出目录: ${outputDir}\n`);

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`✅ 创建输出目录: ${outputDir}`);
} else {
    console.log(`📂 输出目录已存在: ${outputDir}`);
}

// 执行批量转换
const result = converter.batchConvert(inputDir, outputDir);

console.log('\n📊 转换统计:');
console.log(`✅ 成功转换: ${result.successCount} 个文件`);
console.log(`❌ 转换失败: ${result.failedFiles.length} 个文件`);

if (result.failedFiles.length > 0) {
    console.log('\n❌ 失败的文件:');
    result.failedFiles.forEach(file => {
        console.log(`   - ${file}`);
    });
}

// 生成转换报告
const reportPath = path.join(outputDir, 'conversion_report_improved.md');
const reportContent = `# JavaScript to TypeScript 转换报告 (改进版)

## 转换统计
- **总文件数**: ${result.successCount + result.failedFiles.length}
- **成功转换**: ${result.successCount}
- **转换失败**: ${result.failedFiles.length}
- **成功率**: ${((result.successCount / (result.successCount + result.failedFiles.length)) * 100).toFixed(2)}%

## 改进内容
- ✅ 正确提取和格式化方法体
- ✅ 智能清理变量声明
- ✅ 改进代码缩进和格式化
- ✅ 更好的属性提取
- ✅ 优化的父类调用处理

## 转换时间
${new Date().toLocaleString()}

${result.failedFiles.length > 0 ? `## 失败文件列表
${result.failedFiles.map(file => `- ${file}`).join('\n')}

## 建议
失败的文件可能需要手动检查和转换。` : '## 结果\n所有文件转换成功！'}
`;

fs.writeFileSync(reportPath, reportContent, 'utf8');
console.log(`\n📄 转换报告已生成: ${reportPath}`);

console.log('\n🎉 批量转换完成！');
console.log(`📂 请检查 ${outputDir} 目录中的转换结果`);
