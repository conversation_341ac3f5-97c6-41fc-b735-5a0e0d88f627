import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2GameatrCfg from "./GameatrCfg";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2Role from "./Role";
import $2PropertyVo from "./PropertyVo";
import $2ItemModel from "./ItemModel";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Prop_Equip from "./M20Prop_Equip";
import $2MBackpackHero from "./MBackpackHero";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class BackpackHeroHome extends $2Role.default {
    // TODO: 添加属性和方法
}
