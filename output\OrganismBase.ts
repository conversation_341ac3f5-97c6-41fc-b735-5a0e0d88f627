import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2GameatrCfg from "./GameatrCfg";
import $2Notifier from "./Notifier";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2Buff from "./Buff";
import $2SkillManager from "./SkillManager";
import $2PropertyVo from "./PropertyVo";
import $2BaseEntity from "./BaseEntity";
import $2MoveEntity from "./MoveEntity";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class OrganismBase extends $2MoveEntity.default {
    // TODO: 添加属性和方法
}
