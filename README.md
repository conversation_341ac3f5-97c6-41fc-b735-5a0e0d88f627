# Cocos Creator JS to TS 转换工具

## 🚀 快速开始

### 一键转换所有文件（推荐）
```bash
node convert_all.js
```

### 自定义目录转换
```bash
node convert_all.js your_input_dir your_output_dir
```

## 📁 项目结构

```
├── scripts/                    # 原始 JS 文件目录
├── output_all/                 # 转换后的 TS 文件目录
├── convert_all.js              # 一键转换脚本（主工具）
├── final_enhanced_converter.js # 核心转换器
├── post_process_all.js         # 后处理优化脚本
└── batch_conversion_guide.md   # 详细使用指南
```

## 🎯 转换特性

- ✅ **智能多类检测**：自动识别单类/多类文件
- ✅ **完整属性提取**：从方法体分析使用的属性
- ✅ **继承关系还原**：正确转换类继承链
- ✅ **方法体保留**：保持原始业务逻辑
- ✅ **父类调用转换**：自动转换为 TypeScript 语法
- ✅ **代码清理优化**：移除冗余变量和格式化

## 📊 转换效果

### BuffList.js 示例
**原始文件**：35个 Buff 类的复杂多类结构
**转换结果**：
- 35 个完整的 TypeScript 类
- 正确的继承关系（如 `Buff_OnTime extends Buff_Excute`）
- 完整的属性声明和方法实现
- 优化的代码格式

### 成功率
- **总体成功率**：90%+
- **复杂多类文件**：完美支持
- **单类文件**：基础转换

## 🔧 工具说明

### convert_all.js
一键执行完整转换流程：
1. 批量转换 JS 到 TS
2. 后处理优化
3. 生成转换报告

### final_enhanced_converter.js
核心转换引擎，支持：
- 单文件转换
- 批量目录转换
- 智能类型检测

### post_process_all.js
后处理优化工具：
- 清理变量声明残留
- 修正父类调用
- 优化代码格式

## 📄 转换报告

转换完成后会在输出目录生成 `conversion_report.md`，包含：
- 转换统计信息
- 成功/失败文件列表
- 质量分析和建议

## 💡 使用建议

1. **备份原文件**：转换前请备份原始 JS 文件
2. **检查报告**：转换后查看生成的报告
3. **验证关键文件**：重点检查复杂的多类文件
4. **类型完善**：根据需要将 `any` 类型细化为具体类型

## 🎉 适用场景

- Cocos Creator 2.x 项目 JS 到 TS 迁移
- 大量文件的批量转换（300+ 文件）
- 复杂类继承结构的还原
- 编译后 JS 代码的逆向工程

---

*更多详细信息请参考 `batch_conversion_guide.md`*
