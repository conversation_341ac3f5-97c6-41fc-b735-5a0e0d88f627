import $2MVC from "./MVC";
import $2ModeDragonWarModel from "./ModeDragonWarModel";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2UIManager from "./UIManager";
import $2CallID from "./CallID";
import $2NotifyID from "./NotifyID";
import $2Time from "./Time";

const { ccclass, property, menu } = cc._decorator;

export default class ModeDragonWarController extends $2MVC.MVC.MController {
    // TODO: 添加属性和方法
}
