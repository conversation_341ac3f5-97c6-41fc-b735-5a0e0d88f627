import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2StateMachine from "./StateMachine";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Buff from "./Buff";
import $2SkillManager from "./SkillManager";
import $2PetState from "./PetState";
import $2PropertyVo from "./PropertyVo";
import $2BaseEntity from "./BaseEntity";
import $2SteeringBehaviors from "./SteeringBehaviors";
import $2Vehicle from "./Vehicle";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Pet extends $2Vehicle.default {
    // TODO: 添加属性和方法
}
