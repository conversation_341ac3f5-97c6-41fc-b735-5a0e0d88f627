import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2PropertyVo from "./PropertyVo";
import $2Buff from "./Buff";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export class Buff_Default extends $2Buff.Buff.BuffItem {
}

@ccclass
export class Buff_Excute extends $2Buff.Buff.BuffItem {
    _excuteDt: any;
    _isActive: any;
    excuteTime: any;
    otherValue: any;

    constructor() {
        super();
        this.excuteTime = 1;
        this._excuteDt = 0;
    }

    onLoad() {
        this.otherValue && this.otherValue[0] && (this.excuteTime = Math.max(1, this.otherValue[0]));
        this.excute();
    }

    onUpdate(t) {
        if (this._isActive) {
            if ((this._excuteDt += t) > this.excuteTime) {
                this._excuteDt = 0, this.excute();
            }
        super.onUpdate(t);
        }
    }

}

@ccclass
export class Buff_OnTime extends Buff_Excute {
    excuteTime: any;
    otherValue: any;

    onLoad() {
        this.otherValue && this.otherValue[0] && (this.excuteTime = this.otherValue[0]);
        this.setLayer(0);
    }

    excute() {
        this.addLayer();
    }

}

@ccclass
export class Buff_Effect extends $2Buff.Buff.BuffItem {
    cutEffectPath: any;
    cutVo: any;
    owner: any;

    onLoad() {
        var t = this.owner.ID;
        var o = "entity/fight/effect/Buff_" + this.cutVo.id;
        this.cutEffectPath.push(o);
        $2Game.Game.Mgr.instance.showEffectByType(o, cc.Vec2.ZERO, true, -1, {
            parent: this.owner.topEffectBox
        }).then(function (o) {
        if (this.owner.isDead || t != this.owner.ID) {
            return o.removeEntityToUpdate();
        }
        this.cutEffect.push(o.node);
        });
        1001 == this.cutVo.id && (this.owner.roleNode.color = cc.color("#3EDCFF"));
    }

    unload() {
        super.unload();
        1001 == this.cutVo.id && (this.owner.roleNode.color = cc.Color.WHITE);
    }

}

@ccclass
export class Buff_OnSpawnHurt extends $2Buff.Buff.BuffItem {
    cutVo: any;
    isWeight: any;
    listener: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_SpawnHurt, this.onSpawnHurt, this);
    }

    onLoad() {
        this.cutVo.isOverlay > 1 && this.setLayer(0);
    }

    onSpawnHurt() {
        this.isWeight && this.addLayer();
    }

}

@ccclass
export class Buff_OnVampirism extends $2Buff.Buff.BuffItem {
    listener: any;

    onLoad() {
        this.setLayer(0);
    }

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_Vampirism, this.onVampirism, this);
    }

    onVampirism() {
        this.addLayer();
    }

}

@ccclass
export class Buff_CurrencyReward extends $2Buff.Buff.BuffItem {
    cutVo: any;
    game: any;
    otherValue: any;

    onLoad() {
        this.excute();
    }

    addLayer() {
        super.addLayer();
        this.excute();
    }

    excute() {
        var e = this.otherValue[0];
        this.game.knapsackMgr.addGoods(e, this.cutVo.value[0][0]);
        $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得%s %d", $2Cfg.Cfg.CurrencyConfig.get(e).name, this.cutVo.value[0][0]), {
            currencyID: e
        });
    }

}

@ccclass
export class Buff_OnBehit extends $2Buff.Buff.BuffItem {
    cutVo: any;
    isWeight: any;
    listener: any;
    specialMap: any;

    onLoad() {
        this.cutVo.isOverlay > 1 && this.setLayer(0);
    }

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
    }

    onBeHit(e) {
        if (this.isWeight) {
            this.addLayer();
            this.specialMap.filter(function (e) {
                return e.type == $2GameatrCfg.GameatrDefine.useSkill;
            }).forEach(function (o) {
            this.owner.skillMgr.useSubSkill(o.data, $2GameSeting.GameSeting.Release.OnBehit, {
                pos: e.owner.position
            });
        });
        }
    }

}

@ccclass
export class Buff_OnKill extends $2Buff.Buff.BuffItem {
    listener: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_Kill, this.onKill, this);
    }

}

@ccclass
export class Buff_HPLink extends $2Buff.Buff.BuffItem {
    listener: any;
    otherValue: any;
    specialMap: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
    }

    onBeHit() {
        this.listener.curHpProgress < this.otherValue[0] && this.excute();
    }

    excute() {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
        this.specialMap.filter(function (e) {
            return this.type == $2GameatrCfg.GameatrDefine.addObjectBuff;
        }).forEach(function (t) {
        var o;
        null === (o = this.listener) || o === undefined || o.addBuff(t.data[0]);
        });
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
    }

}

@ccclass
export class Buff_ContinuousRecovery extends Buff_Excute {
    attrMap: any;
    caster: any;
    owner: any;

    excute() {
        var e = Math.ceil(this.owner.maxAllHp * this.attrMap.getor($2GameatrCfg.GameatrDefine.recover, 0));
        if (e) {
            this.owner.getHurt().set({
                baseVal: Math.abs(e),
                critRate: 0
            }).hitPos.set(this.owner.position);
        this.caster && (t.owner = this.caster);
        if (e > 0) {
            this.owner.treat(t);
        } else {
        this.owner.behit(t);
        }
        }
    }

}

@ccclass
export class Buff_HPLinkOnce extends Buff_HPLink {
    buffLayer: any;
    game: any;
    unuseLayer: any;

    changeListener(t) {
        super.changeListener(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    }

    onFight_RoundState() {
        1 == this.game.bronMonsterMgr.cutStatus && this.addLayer();
    }

    excute() {
        if (0 != this.buffLayer) {
            super.excute();
            this.unuseLayer();
        }
    }

}

@ccclass
export class Buff_EntityDead extends $2Buff.Buff.BuffItem {
    attrMap: any;
    caster: any;
    cutVo: any;
    game: any;
    isWeight: any;
    listener: any;
    owner: any;
    showBuffEffect: any;
    targetCamp: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_Dead, this.onDead, this);
    }

    onDead() {
        var e;
        if (this.isWeight) {
            var t = this.attrMap.getor($2GameatrCfg.GameatrDefine.monsterSplit, 0);
            var o = this.owner.position.clone();
            if (t > 0) {
                var i = this.owner.lvCfg;
                var n = Object.assign(Object.assign({}, i), {
                    bronTime: .2,
                    bronSpeed: 1,
                    exp: 0,
                    monId: [this.cutVo.value[0][1]],
                    Count: this.cutVo.value[0][0],
                    monsterID: this.cutVo.value[0][1],
                    dropExpRatio: null
                });
            var a = $2GameUtil.GameUtil.getDesignSize.width / this.game.gameCamera.cutZoomRatio * .4;
            for (var s = 0; s < n.Count; s++) {
                o.add($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), 50)).x = cc.misc.clampf(l.x, -a, a);
                this.game.bronMonsterMgr.createMonster(n, l);
            }
        }
        var u = this.attrMap.getor($2GameatrCfg.GameatrDefine.areaDam, 0);
        if (u) {
            var p = this.caster ? Math.ceil(this.caster.property.cut.atk * (this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0) + u)) : 1;
            (null === (e = this.caster) || e === undefined ? undefined : e.buffMgr) && (p *= 1 + this.caster.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.skilldam, 0));
            this.owner.getHurt().set({
                baseVal: p,
                hasBehitEffect: false
            }).hitPos.set(this.owner.position);
        this.game.LatticeElementMap.seekByPos({
            pos: this.owner.position,
            targetCamp: this.targetCamp,
            radius: this.cutVo.effectArea
        }).forEach(function (e) {
        e.behit(f);
        });
        }
        this.showBuffEffect();
        }
    }

}

@ccclass
export class Buff_VicinityHurt extends Buff_Excute {
    attrMap: any;
    caster: any;
    cutVo: any;
    game: any;
    owner: any;
    targetCamp: any;

    excute() {
        var e;
        var t = this.caster ? Math.ceil(this.caster.property.cut.atk * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0)) : 1;
        (null === (e = this.caster) || e === undefined ? undefined : e.buffMgr) && (t *= 1 + this.caster.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.skilldam, 0));
        this.owner.getHurt().set({
            baseVal: t,
            hasBehitEffect: false
        }).hitPos.set(this.owner.position);
        this.game.LatticeElementMap.seekByPos({
            pos: this.owner.position,
            targetCamp: this.targetCamp,
            radius: this.cutVo.effectArea
        }).forEach(function (e) {
        e.behit(o);
        });
    }

}

@ccclass
export class Buff_Halo extends Buff_Excute {
    cutVo: any;
    excuteTime: any;

    onLoad() {
        this.cutVo.attr.forEach(function (t) {
            this.attrMap.set(t, 0);
        });
    }

    unload() {
        super.unload();
    }

    excute() {
        var o = {
            id: 100 * t.id,
            name: t.name + "光环属性",
            type: t.type,
            time: this.excuteTime,
            attr: t.attr,
            value: t.value,
            ishideUI: 1,
            skillId: t.skillId,
            icon: t.icon
        };
        $2Game.Game.mgr.elementMap.forEach(function (i) {
            i.isActive && (t.effectArea && cc.Vec2.squaredDistance(this.owner.position, i.position) > Math.pow(t.effectArea, 2) || (1 == t.object && i.campType != this.owner.campType ? i.addBuffByData(o) : 2 == t.object && i.campType == this.owner.campType ? i.addBuffByData(o) : 3 == t.object && i.addBuffByData(o)));
        });
    }

}

@ccclass
export class Buff_Hurt extends Buff_Excute {
    attrMap: any;
    caster: any;
    cutVo: any;
    hurtVal: any;
    owner: any;
    showBuffEffect: any;

    onLoad() {
        super.onLoad();
    }

    excute() {
        var e;
        this.hurtVal = this.caster ? Math.ceil(this.caster.property.cut.atk * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0)) : 1;
        (null === (e = this.caster) || e === undefined ? undefined : e.buffMgr) && (this.hurtVal *= 1 + this.caster.buffMgr.getSpecificBuffAttr({
            buffID: this.cutVo.id
        }).getor($2GameatrCfg.GameatrDefine.buffDam, 0));
        this.caster.getHurt().set({
            baseVal: this.hurtVal,
            owner: this.caster,
            hasBehitEffect: false
        }).hitPos.set(this.owner.position);
        this.owner.behit(t);
        this.showBuffEffect();
    }

    unload() {
        super.unload();
    }

}

@ccclass
export class Buff_OnUseSkillHurt extends $2Buff.Buff.BuffItem {
    attrMap: any;
    hurt: any;
    listener: any;
    onEntityUseSkill: any;
    owner: any;
    role: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onEntityUseSkill, this);
    }

    onEntityUseSkill() {
        this.hurt.baseVal = this.role.maxAllHp * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0);
        this.hurt.hitPos.set(this.owner.position);
        this.role.behit(this.hurt);
    }

    onLoad() {
        super.onLoad();
        this.hurt = this.owner.getHurt().set({
            baseVal: 0,
            owner: this.owner
        });
    }

    unload() {
        super.unload();
        this.hurt = null;
    }

}

@ccclass
export class Buff_SubSkill extends $2Buff.Buff.BuffItem {
}

@ccclass
export class Buff_AtkFocus extends Buff_OnSpawnHurt {
    oldVictim: any;

    onLoad() {
        this.setLayer(0);
        this.oldVictim = null;
    }

    onSpawnHurt(e, t) {
        t.node.once($2ListenID.ListenID.Fight_Dead, function () {
            this.setLayer(0);
            this.oldVictim = null;
        }, t.node);
        if (null == this.oldVictim || this.oldVictim == t.ID) {
            this.addLayer();
        } else {
        this.setLayer(0);
        }
        this.oldVictim = t.ID;
    }

}

@ccclass
export class Buff_AdrenalTechnology extends Buff_OnSpawnHurt {
    attrMap: any;
    cutVo: any;
    game: any;
    isWeight: any;

    onLoad() {
        };
        t.prototype.onSpawnHurt = function () {
            if (this.isWeight) {
                var o = {
                    id: 100 * t.id,
                    name: t.name + "光环属性",
                    type: 3,
                    time: t.otherValue[0],
                    attr: [$2GameatrCfg.GameatrDefine.skilldam],
                    value: [[this.attrMap.get($2GameatrCfg.GameatrDefine.damadd)]],
                    ishideUI: 1,
                    res: ["bones/skill/fx_buff_ad", "2"]
                };
            $2Game.Game.mgr.elementMap.forEach(function (t) {
                t.isActive && t.campType == this.owner.campType && t.addBuffByData(o);
            });
        this.game.mainRole.addBuffByData(o);
        }
    }

}

@ccclass
export class Buff_OnSkillUseUnload extends $2Buff.Buff.BuffItem {
    listener: any;
    mgr: any;
    onEntityUseSkill: any;

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onEntityUseSkill, this);
    }

    onEntityUseSkill() {
        this.mgr.clearBuff(this);
    }

}

@ccclass
export class Buff_ReboundDam extends Buff_OnBehit {
    cutVo: any;
    game: any;
    isWeight: any;
    listener: any;
    otherValue: any;
    owner: any;

    onLoad() {
        };
        t.prototype.changeListener = function (e) {
            var t;
            null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
    }

    onBeHit(e) {
        var t;
        if (this.isWeight) {
            var i = e.owner;
            if (i.isActive) {
                var n = this.cutVo.effectArea ? this.game.LatticeElementMap.seekByPos({
                    pos: i.position,
                    targetCamp: [i.campType],
                    radius: this.cutVo.effectArea
                }) : [i];
            this.owner.delayByGame(function () {
                n.forEach(function (e) {
                    var t = e.curHp * this.mgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.ReboundDamPer, 0) + this.attrMap.getor($2GameatrCfg.GameatrDefine.ReboundDamVal, 0);
                    var i = this.owner.getHurt().set({
                        baseVal: Math.abs(t),
                        critRate: 0
                    });
                e.isActive && e.behit(i);
            });
        }, .5);
        }
        var r = $2Cfg.Cfg.BulletEffect.get(null === (t = this.otherValue) || t === undefined ? undefined : t[0]);
        r && $2Manager.Manager.loader.loadSpineNode(r.spine[0], {
            nodeAttr: {
                parent: this.game._topEffectNode,
                position: i.position
            },
        spAttr: {
            defaultAnimation: r.spine[1] || "animation",
            loop: false
        },
        delayRemove: 2
        });
        }
    }

}

@ccclass
export class Buff_OnKillLayout extends Buff_OnKill {
    onLoad() {
        super.onLoad();
        this.setLayer(0);
    }

    onKill() {
        this.addLayer();
    }

}

@ccclass
export class Buff_HitBack extends $2Buff.Buff.BuffItem {
    _hitBackDirection: any;
    _hitBackDis: any;
    attrMap: any;
    caster: any;
    owner: any;

    onLoad() {
        super.onLoad();
        this._hitBackDis = this.attrMap.getor($2GameatrCfg.GameatrDefine.repeled, 0);
        this._hitBackDirection = this.owner.position.sub(this.caster.position).normalize();
        this._hitBackDirection.x = 0;
    }

    onUpdate(t) {
        if (this._hitBackDirection) {
            super.onUpdate(t);
            cc.Vec2.multiplyScalar(_, this._hitBackDirection, this._hitBackDis * t);
            cc.Vec2.add(_, _, this.owner.position);
            this.owner.setPosition(_);
        }
    }

    unload() {
        this._hitBackDirection = null;
        super.unload();
    }

}

@ccclass
export class Buff_OnLifeVal extends Buff_OnBehit {
    otherValue: any;
    owner: any;

    onBeHit() {
        this.setLayer(this.owner.curHpProgress <= this.otherValue[0] ? 1 : 0);
    }

}

@ccclass
export class Buff_OnSpawnHurtAddArmor extends Buff_OnSpawnHurt {
    attrMap: any;
    isWeight: any;
    owner: any;

    onSpawnHurt() {
        if (this.isWeight) {
            var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
            this.owner.addArmor(e);
        }
    }

}

@ccclass
export class Buff_OnBehitAddArmor extends Buff_OnBehit {
    attrMap: any;
    isWeight: any;
    owner: any;

    onBeHit() {
        if (this.isWeight) {
            var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
            this.owner.addArmor(e);
        }
    }

}

@ccclass
export class Buff_RestoreArmor extends Buff_Excute {
    attrMap: any;
    isWeight: any;
    owner: any;

    excute() {
        if (this.isWeight) {
            var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
            this.owner.addArmor(e);
        }
    }

}

@ccclass
export class Buff_OnSkill extends $2Buff.Buff.BuffItem {
    cutVo: any;
    isWeight: any;
    listener: any;

    onLoad() {
        this.cutVo.isOverlay > 1 && this.setLayer(0);
    }

    changeListener(e) {
        var t;
        null === (t = this.listener) || t === undefined || t.node.changeListener(e, $2ListenID.ListenID.Fight_OnSkill, this.onSkill, this);
    }

    onSkill() {
        this.isWeight && this.addLayer();
    }

}

@ccclass
export class Buff_Vampire extends Buff_OnSpawnHurt {
    isWeight: any;
    owner: any;

    onSpawnHurt(e, t) {
        if (this.isWeight && e.val > 0) {
            var o = Math.ceil(e.val * this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.vampireEffect, 0));
            var i = this.owner.getHurt().set({
                baseVal: Math.abs(o),
                critRate: 0
            });
        this.owner.treat(i);
        this.owner.node.emit($2ListenID.ListenID.Fight_Vampirism, i, t);
        }
    }

}

@ccclass
export class Buff_AssociationProp extends $2Buff.Buff.BuffItem {
    pack: any;

    onLoad() {
        super.onLoad();
        var o = this.pack.propList.filter(function (e) {
            return this.otherValue.includes(e.mergeCfg.id);
        });
        o.push.apply(o, this.pack.StoreList.arr);
        this.setLayer(o.length > 0 ? 1 : 0);
    }

}

@ccclass
export class Buff_ResistDamage extends $2Buff.Buff.BuffItem {
    buffLayer: any;
    cutVo: any;
    mgr: any;
    showBuffEffect: any;

    onLoad() {
        this.setLayer(this.cutVo.isOverlay);
    }

    unuseLayer(t) {
        t === undefined && (t = -1);
        this.showBuffEffect();
        super.unuseLayer(t);
        0 == this.buffLayer && this.mgr.clearBuff(this);
    }

}

@ccclass
export class Buff_SetSlash extends $2Buff.Buff.BuffItem {
    caster: any;
    cutVo: any;
    game: any;
    mgr: any;
    owner: any;

    onLoad() {
        var e = this.caster.getHurt().set({
            baseVal: 999999999,
            critRate: 0,
            type: $2PropertyVo.Hurt.Type.Slash
        });
        this.owner.behit(e);
        this.game.showEffectByPath(this.cutVo.res[0], {
            nodeAttr: {
                parent: this.game._topEffectNode,
                position: this.owner.position,
                scale: 1
            },
        spAttr: {
            defaultAnimation: "animation",
            premultipliedAlpha: false,
            loop: false
        },
        delayRemove: 1
        });
        this.mgr.clearBuff(this);
    }

}

@ccclass
export class Buff_OnRoundState extends $2Buff.Buff.BuffItem {
    changeListener(t) {
        super.changeListener(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    }

}

@ccclass
export class Buff_ReplaceRole extends $2Buff.Buff.BuffItem {
    attrMap: any;
    mgr: any;
    owner: any;

    onLoad() {
        var e = this.attrMap.get($2GameatrCfg.GameatrDefine.replaceRole);
        var t = $2Cfg.Cfg.RoleUnlock.get(e);
        var o = $2Game.ModeCfg.Role.find({
            roleId: e,
            lv: 1
        });
        i.skillMgr.clearByID(i.myData.startSkill, true);
        i.myData.startBuff && i.buffMgr.clearBuffByID(i.myData.startBuff);
        i.myData = t;
        i.skillMgr.add(t.startSkill, true, true);
        t.startBuff && i.buffMgr.add(t.startBuff);
        i.property.set(o);
        i.updateProperty();
        i.initHp();
        this.mgr.clearBuff(this);
    }

}
