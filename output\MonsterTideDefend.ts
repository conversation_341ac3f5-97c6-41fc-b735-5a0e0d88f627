import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2StateMachine from "./StateMachine";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2TideDefendModel from "./TideDefendModel";
import $2Game from "./Game";
import $2BaseEntity from "./BaseEntity";
import $2MonsterTidal from "./MonsterTidal";
import $2MTideDefendRebound from "./MTideDefendRebound";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MonsterTideDefend extends $2MonsterTidal.default {
    // TODO: 添加属性和方法
}
