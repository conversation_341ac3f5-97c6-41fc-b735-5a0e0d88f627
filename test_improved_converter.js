const FinalEnhancedConverter = require('./final_enhanced_converter.js');
const path = require('path');

// 创建转换器实例
const converter = new FinalEnhancedConverter();

// 测试单个文件转换
const testFile = './scripts/BuffList.js';
const outputFile = './test_output/BuffList.ts';

console.log('🧪 测试改进后的转换器...\n');
console.log(`📁 输入文件: ${testFile}`);
console.log(`📁 输出文件: ${outputFile}\n`);

// 转换文件
const success = converter.convertFile(testFile, outputFile);

if (success) {
    console.log('\n✅ 转换成功！请检查输出文件的方法体是否正确格式化。');
} else {
    console.log('\n❌ 转换失败！');
}
