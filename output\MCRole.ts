import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2Buff from "./Buff";
import $2BaseEntity from "./BaseEntity";
import $2DragonBody from "./DragonBody";
import $2Role from "./Role";
import $2Game from "./Game";
import $2SkillManager from "./SkillManager";
import $2PropertyVo from "./PropertyVo";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2MCBoss from "./MCBoss";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class MCRole extends $2Role.default {
    // TODO: 添加属性和方法
}
