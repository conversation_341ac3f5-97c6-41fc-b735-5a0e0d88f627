import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2UIManager from "./UIManager";
import $2M20_PartItem from "./M20_PartItem";
import $2M20_ShopPartItem from "./M20_ShopPartItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_box extends $2M20_ShopPartItem.default {
    // TODO: 添加属性和方法
}
