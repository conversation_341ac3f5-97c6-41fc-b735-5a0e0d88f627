const fs = require('fs');
const path = require('path');

class FinalEnhancedConverter {
    constructor() {
        this.methodNames = new Set(['onLoad', 'onUpdate', 'unload', 'excute', 'changeListener', 'onSpawnHurt', 'onBeHit', 'onKill', 'onVampirism', 'addLayer', 'setLayer', 'onSkill', 'onDead', 'onFight_RoundState']);
    }

    // 智能属性提取
    extractProperties(methodBodies, constructorProps = []) {
        const properties = new Set();
        
        // 从构造函数添加属性
        constructorProps.forEach(prop => properties.add(prop.name));
        
        // 从方法体提取属性
        methodBodies.forEach(body => {
            // 匹配 this.property 模式
            const matches = body.match(/this\.(\w+)/g);
            if (matches) {
                matches.forEach(match => {
                    const propName = match.replace('this.', '');
                    // 排除已知的方法名
                    if (!this.methodNames.has(propName) && !propName.includes('(')) {
                        properties.add(propName);
                    }
                });
            }
        });
        
        return Array.from(properties).sort();
    }

    // 智能清理方法体
    cleanMethodBody(body) {
        if (!body || !body.trim()) return '';

        let cleaned = body.trim();

        // 1. 修正父类调用
        cleaned = cleaned
            .replace(/(\w+)\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$2($3)')
            .replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)');

        // 2. 清理变量声明和替换
        cleaned = this.cleanVariableDeclarations(cleaned);

        // 3. 其他优化
        cleaned = cleaned
            .replace(/cc__assign/g, 'Object.assign')
            .replace(/null !== (\w+) && \w+ \|\| /g, '$1 || ')
            .replace(/undefined === (\w+)/g, '$1 === undefined')
            .replace(/null === (\w+)/g, '$1 === null');

        // 4. 格式化代码
        cleaned = this.formatMethodBody(cleaned);

        return cleaned;
    }

    // 清理变量声明
    cleanVariableDeclarations(body) {
        let cleaned = body;

        // 1. 收集所有变量声明
        const varDeclarations = new Map();
        const varPattern = /var\s+(\w+)\s*=\s*([^;]+);/g;
        let match;

        while ((match = varPattern.exec(body)) !== null) {
            const varName = match[1];
            const value = match[2].trim();
            varDeclarations.set(varName, value);
        }

        // 2. 处理this别名
        for (const [varName, value] of varDeclarations) {
            if (value === 'this' && varName.length === 1) {
                // 移除变量声明
                const declRegex = new RegExp(`var\\s+${varName}\\s*=\\s*this;\\s*`, 'g');
                cleaned = cleaned.replace(declRegex, '');

                // 替换所有使用
                const useRegex = new RegExp(`\\b${varName}\\.`, 'g');
                cleaned = cleaned.replace(useRegex, 'this.');
            }
        }

        // 3. 处理其他常见模式
        cleaned = cleaned
            .replace(/var\s+([a-z])\s*=\s*this\.(\w+);\s*/g, '') // 移除 var t = this.something;
            .replace(/var\s+([a-z])\s*=\s*([^;]+);\s*\n\s*\1\./g, '$2.'); // 内联简单变量

        return cleaned;
    }

    // 格式化方法体
    formatMethodBody(body) {
        if (!body) return '';

        // 清理多余的空行和空格
        let cleaned = body
            .replace(/\n\s*\n\s*\n/g, '\n\n') // 移除多余空行
            .replace(/;\s*\n\s*\n/g, ';\n') // 移除语句后的多余空行
            .trim();

        // 分割成行并处理缩进
        const lines = cleaned.split('\n');
        const formattedLines = [];
        let braceLevel = 0;

        for (let line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            // 计算大括号层级变化
            const openBraces = (trimmed.match(/\{/g) || []).length;
            const closeBraces = (trimmed.match(/\}/g) || []).length;

            // 如果行以}开始，先减少缩进
            if (trimmed.startsWith('}')) {
                braceLevel = Math.max(0, braceLevel - 1);
            }

            // 添加适当的缩进
            const indent = '    '.repeat(braceLevel);
            formattedLines.push(indent + trimmed);

            // 更新大括号层级
            braceLevel += openBraces - closeBraces;
            braceLevel = Math.max(0, braceLevel);
        }

        return formattedLines.join('\n');
    }



    // 转换多类文件
    convertMultiClass(content) {
        const imports = this.extractImports(content);
        const classes = this.extractClasses(content);
        
        if (!classes || classes.length === 0) return null;
        
        console.log(`📦 发现 ${classes.length} 个类，正在最终转换...`);
        
        let tsCode = '';
        
        // 导入语句
        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }
        
        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        // 生成类
        classes.forEach((classInfo, index) => {
            tsCode += this.generateFinalClassCode(classInfo);
            if (index < classes.length - 1) {
                tsCode += '\n';
            }
        });
        
        return tsCode;
    }

    // 生成最终的类代码
    generateFinalClassCode(classInfo) {
        const baseClass = classInfo.baseClass || '$2Buff.Buff.BuffItem';
        let code = `@ccclass\nexport class ${classInfo.className} extends ${baseClass} {\n`;
        
        // 收集所有方法体用于属性提取
        const methodBodies = classInfo.methods ? classInfo.methods.map(m => m.body || '') : [];
        const constructorProps = classInfo.constructor ? classInfo.constructor.properties : [];
        
        // 提取并声明属性
        const properties = this.extractProperties(methodBodies, constructorProps);
        if (properties.length > 0) {
            properties.forEach(prop => {
                code += `    ${prop}: any;\n`;
            });
            code += '\n';
        }
        
        // 构造函数
        if (classInfo.constructor && classInfo.constructor.properties && classInfo.constructor.properties.length > 0) {
            code += '    constructor() {\n';
            code += '        super();\n';
            classInfo.constructor.properties.forEach(prop => {
                code += `        this.${prop.name} = ${prop.value};\n`;
            });
            code += '    }\n\n';
        }
        
        // 方法
        if (classInfo.methods) {
            classInfo.methods.forEach(method => {
                const params = method.params ? method.params.join(', ') : '';
                code += `    ${method.name}(${params}) {\n`;

                const cleanedBody = this.cleanMethodBody(method.body);
                if (cleanedBody) {
                    // 直接添加已经格式化的方法体，每行前面加上8个空格（2级缩进）
                    const lines = cleanedBody.split('\n');
                    lines.forEach(line => {
                        if (line.trim()) {
                            code += `        ${line}\n`;
                        }
                    });
                } else {
                    code += '        // TODO: 实现方法体\n';
                }

                code += '    }\n\n';
            });
        }
        
        code += '}\n';
        return code;
    }

    // 提取导入
    extractImports(content) {
        const imports = [];
        const regex = /var (\$\d*\$?\d*\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            imports.push(`import ${match[1]} from "./${match[2]}";`);
        }

        return imports;
    }

    // 提取类
    extractClasses(content) {
        const classes = [];
        const lines = content.split('\n');
        let currentClass = null;
        let classContent = [];
        let braceCount = 0;
        let inClass = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            const classMatch = line.match(/var (exp_\w+) = function \(e\) \{/);
            if (classMatch) {
                if (currentClass) {
                    this.processClass(currentClass, classContent, classes);
                }
                
                currentClass = {
                    fullVarName: classMatch[1],
                    className: classMatch[1].replace('exp_', ''),
                    startLine: i
                };
                classContent = [line];
                braceCount = 1;
                inClass = true;
                continue;
            }
            
            if (inClass) {
                classContent.push(line);
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;
                
                if (braceCount === 0 && line.includes('}(')) {
                    currentClass.baseClass = this.determineBaseClass(line);
                    this.processClass(currentClass, classContent, classes);
                    currentClass = null;
                    classContent = [];
                    inClass = false;
                }
            }
        }
        
        return classes;
    }

    // 确定基类
    determineBaseClass(line) {
        let baseMatch = line.match(/\}(\(\$\d+\w+(?:\.\w+)*\));/);
        if (baseMatch) {
            return baseMatch[1].replace(/[()]/g, '');
        }
        
        baseMatch = line.match(/\}\((exp_\w+)\);/);
        if (baseMatch) {
            return baseMatch[1].replace('exp_', '');
        }
        
        return '$2Buff.Buff.BuffItem';
    }

    // 处理类
    processClass(classInfo, classContent, classes) {
        const fullContent = classContent.join('\n');
        const details = this.extractClassDetails(fullContent);
        
        classes.push({
            ...classInfo,
            ...details
        });
    }

    // 提取类详情
    extractClassDetails(classContent) {
        const details = {
            constructor: null,
            methods: [],
            decorators: []
        };

        if (classContent.includes('ccp_ccclass')) {
            details.decorators.push('@ccclass');
        }

        // 构造函数
        const constructorMatch = classContent.match(/function ([_\w]+)\(\) \{([\s\S]*?)\n  \}/);
        if (constructorMatch) {
            details.constructor = this.parseConstructor(constructorMatch[2], constructorMatch[1]);
        }

        // 方法
        const methodPattern = /([_\w]+)\.prototype\.(\w+) = function \(([^)]*)\) \{([\s\S]*?)\n  \};/g;
        let methodMatch;
        
        while ((methodMatch = methodPattern.exec(classContent)) !== null) {
            details.methods.push({
                name: methodMatch[2],
                params: methodMatch[3] ? methodMatch[3].split(',').map(p => p.trim()).filter(p => p) : [],
                body: methodMatch[4]
            });
        }

        return details;
    }

    // 解析构造函数
    parseConstructor(constructorBody, constructorVar) {
        const properties = [];
        const propPattern = /(\w+)\.(\w+) = ([^;]+);/g;
        let match;
        
        while ((match = propPattern.exec(constructorBody)) !== null) {
            if (match[1] === constructorVar || match[1] === 't' || match[1] === '_ctor') {
                properties.push({
                    name: match[2],
                    value: match[3]
                });
            }
        }
        
        return { properties };
    }

    // 转换文件
    convertFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');

            // 检测多类
            const isMultiClass = (jsContent.match(/var exp_\w+ = function \(e\) \{/g) || []).length > 1;

            let tsContent = null;
            if (isMultiClass) {
                tsContent = this.convertMultiClass(jsContent);
            } else {
                // 单类处理
                tsContent = this.convertSingleClass(jsContent);
            }

            if (tsContent) {
                // 确保输出目录存在
                const outputDir = path.dirname(outputPath);
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir, { recursive: true });
                }

                fs.writeFileSync(outputPath, tsContent, 'utf8');
                console.log(`✅ ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
                return true;
            }

            return false;
        } catch (error) {
            console.error(`❌ ${path.basename(inputPath)}: ${error.message}`);
            return false;
        }
    }

    // 批量转换目录
    batchConvert(inputDir, outputDir) {
        if (!fs.existsSync(inputDir)) {
            console.error(`❌ 输入目录不存在: ${inputDir}`);
            return { successCount: 0, failedFiles: [] };
        }

        // 确保输出目录存在
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        let successCount = 0;
        const failedFiles = [];

        console.log(`🚀 开始批量转换 ${files.length} 个文件...\n`);

        files.forEach(file => {
            const inputPath = path.join(inputDir, file);
            const outputPath = path.join(outputDir, file.replace('.js', '.ts'));

            if (this.convertFile(inputPath, outputPath)) {
                successCount++;
            } else {
                failedFiles.push(file);
            }
        });

        console.log(`\n🎉 批量转换完成!`);
        console.log(`✅ 成功: ${successCount}/${files.length}`);

        if (failedFiles.length > 0) {
            console.log(`❌ 失败文件: ${failedFiles.join(', ')}`);
        }

        return { successCount, failedFiles };
    }

    // 简单的单类转换
    convertSingleClass(content) {
        const imports = this.extractImports(content);

        // 尝试提取单类信息
        let classMatch = content.match(/var def_(\w+) = function \(e\) \{/);
        if (!classMatch) {
            classMatch = content.match(/var exp_(\w+) = function \(e\) \{/);
        }

        if (!classMatch) {
            return null; // 无法识别的文件格式
        }

        const className = classMatch[1];

        // 查找继承关系
        const extendsMatch = content.match(/\}\((\$\d+\w+)\.(\w+)\.(\w+)\);/) ||
                            content.match(/\}\((\$\d+\w+)\.(\w+)\);/) ||
                            content.match(/\}\((\$\d*\$?\d*\w+)\.(\w+)\);/);

        let baseClass = 'cc.Component';
        if (extendsMatch) {
            if (extendsMatch[3]) {
                baseClass = `${extendsMatch[1]}.${extendsMatch[2]}.${extendsMatch[3]}`;
            } else {
                baseClass = `${extendsMatch[1]}.${extendsMatch[2]}`;
            }
        }

        // 生成基础的单类代码
        let tsCode = '';

        if (imports.length > 0) {
            tsCode += imports.join('\n') + '\n\n';
        }

        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';

        // 检查装饰器
        if (content.includes('ccp_ccclass')) {
            tsCode += '@ccclass\n';
        }

        tsCode += `export default class ${className} extends ${baseClass} {\n`;
        tsCode += '    // TODO: 添加属性和方法\n';
        tsCode += '}\n';

        return tsCode;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new FinalEnhancedConverter();

    const args = process.argv.slice(2);
    const input = args[0] || './scripts';
    const output = args[1] || './output_final_enhanced';

    console.log(`📁 输入: ${input}`);
    console.log(`📁 输出: ${output}\n`);

    // 检查输入是文件还是目录
    if (fs.existsSync(input)) {
        const stats = fs.statSync(input);

        if (stats.isFile()) {
            // 单文件转换
            converter.convertFile(input, output);
        } else if (stats.isDirectory()) {
            // 批量转换
            const result = converter.batchConvert(input, output);

            if (result.failedFiles.length > 0) {
                console.log(`\n💡 提示: 失败的文件可能需要手动处理`);
                console.log(`📄 失败文件列表: ${result.failedFiles.join(', ')}`);
            }

            console.log(`\n✨ 转换完成! 请检查 ${output} 目录`);
        }
    } else {
        console.error(`❌ 输入路径不存在: ${input}`);
    }
}

module.exports = FinalEnhancedConverter;
