import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_Pop_ShopBuyConfirm extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
