import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Pop from "./Pop";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ItemModel from "./ItemModel";
import $2MoreGamesView from "./MoreGamesView";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M33_Pop_GameEnd extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
