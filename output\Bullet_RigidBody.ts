import $2CallID from "./CallID";
import $2SoundCfg from "./SoundCfg";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2BaseEntity from "./BaseEntity";
import $2Game from "./Game";
import $2BulletBase from "./BulletBase";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Bullet_RigidBody extends $2BulletBase.default {
    // TODO: 添加属性和方法
}
