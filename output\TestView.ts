import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Pop from "./Pop";
import $2Launcher from "./Launcher";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2StorageID from "./StorageID";
import $2FColliderManager from "./FColliderManager";
import $2GameUtil from "./GameUtil";
import $2UserVo from "./UserVo";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2MBackpackHero from "./MBackpackHero";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2ModeChainsModel from "./ModeChainsModel";
import $2SettingModel from "./SettingModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class TestView extends $2Pop.Pop {
    // TODO: 添加属性和方法
}
