import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2RewardEvent from "./RewardEvent";
import $2SkillModel from "./SkillModel";
import $2BaseEntity from "./BaseEntity";
import $2Vehicle from "./Vehicle";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class NPC extends $2Vehicle.default {
    // TODO: 添加属性和方法
}
