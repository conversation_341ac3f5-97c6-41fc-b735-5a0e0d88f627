import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2StateMachine from "./StateMachine";
import $2GameUtil from "./GameUtil";
import $2Game from "./Game";
import $2MonsterState from "./MonsterState";
import $2PropertyVo from "./PropertyVo";
import $2BaseEntity from "./BaseEntity";
import $2SteeringBehaviors from "./SteeringBehaviors";
import $2Vehicle from "./Vehicle";
import $2Manager from "./Manager";

const { ccclass, property, menu } = cc._decorator;

export default class Monster extends $2Vehicle.default {
    // TODO: 添加属性和方法
}
