import $2Cfg from "./Cfg";
import $2GameatrCfg from "./GameatrCfg";
import $2Manager from "./Manager";
import $2Intersection from "./Intersection";
import $2GameUtil from "./GameUtil";
import $2NodePool from "./NodePool";
import $2Buff from "./Buff";
import $2Game from "./Game";
import $2DragonBody from "./DragonBody";
import $2OrganismBase from "./OrganismBase";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class Dragon extends $2OrganismBase.default {
    // TODO: 添加属性和方法
}
